<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="800" viewBox="0 0 1200 800">
  <!-- خلفية متدرجة -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a237e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4285F4;stop-opacity:1" />
    </linearGradient>
    
    <!-- فلتر التمويه -->
    <filter id="blur1" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
    </filter>
    
    <filter id="blur2" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="8" />
    </filter>
    
    <filter id="blur3" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="12" />
    </filter>
  </defs>
  
  <!-- خلفية متدرجة -->
  <rect width="1200" height="800" fill="url(#grad1)" />
  
  <!-- بطاقات مبهمة -->
  <!-- صف 1 -->
  <rect x="100" y="100" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.2)" filter="url(#blur2)" transform="rotate(15 100 100)" />
  <rect x="300" y="50" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.15)" filter="url(#blur1)" transform="rotate(-10 300 50)" />
  <rect x="500" y="150" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.25)" filter="url(#blur3)" transform="rotate(5 500 150)" />
  <rect x="700" y="80" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.2)" filter="url(#blur2)" transform="rotate(-5 700 80)" />
  <rect x="900" y="120" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.15)" filter="url(#blur1)" transform="rotate(12 900 120)" />
  
  <!-- صف 2 -->
  <rect x="50" y="350" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.15)" filter="url(#blur1)" transform="rotate(-8 50 350)" />
  <rect x="250" y="320" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.25)" filter="url(#blur3)" transform="rotate(7 250 320)" />
  <rect x="450" y="380" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.2)" filter="url(#blur2)" transform="rotate(-12 450 380)" />
  <rect x="650" y="330" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.15)" filter="url(#blur1)" transform="rotate(9 650 330)" />
  <rect x="850" y="370" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.25)" filter="url(#blur3)" transform="rotate(-7 850 370)" />
  <rect x="1050" y="340" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.2)" filter="url(#blur2)" transform="rotate(11 1050 340)" />
  
  <!-- صف 3 -->
  <rect x="120" y="600" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.25)" filter="url(#blur3)" transform="rotate(9 120 600)" />
  <rect x="320" y="580" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.15)" filter="url(#blur1)" transform="rotate(-5 320 580)" />
  <rect x="520" y="620" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.2)" filter="url(#blur2)" transform="rotate(7 520 620)" />
  <rect x="720" y="590" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.25)" filter="url(#blur3)" transform="rotate(-10 720 590)" />
  <rect x="920" y="610" width="150" height="200" rx="10" ry="10" fill="rgba(255,255,255,0.15)" filter="url(#blur1)" transform="rotate(6 920 610)" />
  
  <!-- نقاط متوهجة -->
  <circle cx="200" cy="200" r="5" fill="rgba(255,255,255,0.8)" filter="url(#blur1)" />
  <circle cx="600" cy="300" r="7" fill="rgba(255,255,255,0.7)" filter="url(#blur1)" />
  <circle cx="1000" cy="250" r="6" fill="rgba(255,255,255,0.8)" filter="url(#blur1)" />
  <circle cx="300" cy="500" r="8" fill="rgba(255,255,255,0.7)" filter="url(#blur1)" />
  <circle cx="800" cy="550" r="5" fill="rgba(255,255,255,0.8)" filter="url(#blur1)" />
  <circle cx="500" cy="700" r="7" fill="rgba(255,255,255,0.7)" filter="url(#blur1)" />
  <circle cx="1100" cy="650" r="6" fill="rgba(255,255,255,0.8)" filter="url(#blur1)" />
</svg>