{% extends 'base.html' %}

{% block title %}تصميم البطاقات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .designer-container {
        padding: 2rem 0;
    }
    
    .card-designer-title {
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 10px;
        padding: 3rem;
        text-align: center;
        margin-bottom: 2rem;
        transition: all 0.3s;
        background-color: #f9f9f9;
    }
    
    .upload-area:hover, .upload-area.dragover {
        border-color: #4285F4;
        background-color: #f0f7ff;
    }
    
    .upload-icon {
        font-size: 3rem;
        color: #4285F4;
        margin-bottom: 1rem;
    }
    
    .upload-text {
        margin-bottom: 1.5rem;
    }
    
    .canvas-container {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: auto;
        margin-bottom: 2rem;
        position: relative;
    }
    
    .a4-page {
        width: 210mm;
        height: 297mm;
        background-color: white;
        margin: 2rem auto;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        position: relative;
    }
    
    .card-item {
        position: absolute;
        cursor: move;
        border: 2px solid transparent;
        transition: border 0.2s;
    }
    
    .card-item:hover {
        border: 2px solid #4285F4;
    }
    
    .card-item img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    
    .card-item .remove-btn {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: red;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        cursor: pointer;
        display: none;
    }
    
    .card-item:hover .remove-btn {
        display: block;
    }
    
    .card-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .card-thumbnail {
        width: 100%;
        height: 100px;
        object-fit: cover;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .card-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .card-controls {
        position: sticky;
        bottom: 0;
        background-color: white;
        padding: 1rem 0;
        border-top: 1px solid #eee;
        z-index: 100;
    }
    
    .dimensions-input {
        width: 80px;
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        flex-direction: column;
    }
    
    .spinner-border {
        width: 3rem;
        height: 3rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container designer-container">
    <h1 class="card-designer-title">تصميم وترتيب البطاقات</h1>
    
    <div class="row">
        <div class="col-lg-8">
            <!-- منطقة العرض والتصميم -->
            <div class="canvas-container" id="canvasContainer">
                <div class="a4-page" id="a4Page">
                    <!-- سيتم إضافة البطاقات هنا بواسطة JavaScript -->
                </div>
            </div>
            
            <div class="card-controls">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <label for="cardWidth" class="me-2">العرض (مم):</label>
                            <input type="number" id="cardWidth" class="form-control dimensions-input me-3" value="85">
                            
                            <label for="cardHeight" class="me-2">الارتفاع (مم):</label>
                            <input type="number" id="cardHeight" class="form-control dimensions-input" value="55">
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button id="autoArrangeBtn" class="btn btn-secondary me-2">
                            <i class="fas fa-th"></i> ترتيب تلقائي
                        </button>
                        <button id="generatePdfBtn" class="btn btn-primary">
                            <i class="fas fa-file-pdf"></i> إنشاء PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- منطقة الرفع والبطاقات المتاحة -->
            <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <h3 class="upload-text">اسحب وأفلت صور البطاقات هنا</h3>
                <p>أو</p>
                <input type="file" id="fileInput" class="d-none" accept=".jpg,.jpeg,.png" multiple>
                <button id="browseBtn" class="btn btn-primary">
                    <i class="fas fa-folder-open"></i> استعراض الملفات
                </button>
            </div>
            
            <h4>البطاقات المرفوعة</h4>
            <div class="card-list" id="cardList">
                <div class="row g-2" id="thumbnailsContainer">
                    <!-- سيتم إضافة الصور المصغرة هنا بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قالب عنصر البطاقة -->
<template id="cardItemTemplate">
    <div class="card-item">
        <img src="" alt="بطاقة">
        <div class="remove-btn">
            <i class="fas fa-times"></i>
        </div>
    </div>
</template>

<!-- قالب الصورة المصغرة -->
<template id="thumbnailTemplate">
    <div class="col-6">
        <img src="" alt="بطاقة" class="card-thumbnail">
    </div>
</template>

<!-- شاشة التحميل -->
<div class="loading-overlay d-none" id="loadingOverlay">
    <div class="spinner-border text-light" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
    </div>
    <h4 id="loadingText">جاري المعالجة...</h4>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdnjs.cloudflare.com/ajax/libs/interact.js/1.10.11/interact.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // المتغيرات العامة
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const browseBtn = document.getElementById('browseBtn');
        const thumbnailsContainer = document.getElementById('thumbnailsContainer');
        const a4Page = document.getElementById('a4Page');
        const cardItemTemplate = document.getElementById('cardItemTemplate');
        const thumbnailTemplate = document.getElementById('thumbnailTemplate');
        const autoArrangeBtn = document.getElementById('autoArrangeBtn');
        const generatePdfBtn = document.getElementById('generatePdfBtn');
        const cardWidthInput = document.getElementById('cardWidth');
        const cardHeightInput = document.getElementById('cardHeight');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingText = document.getElementById('loadingText');
        
        // مصفوفة لتخزين البطاقات المرفوعة
        let uploadedCards = [];
        
        // إعداد منطقة السحب والإفلات
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            uploadArea.classList.add('dragover');
        }
        
        function unhighlight() {
            uploadArea.classList.remove('dragover');
        }
        
        // معالجة إفلات الملفات
        uploadArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        // معالجة النقر على زر استعراض الملفات
        browseBtn.addEventListener('click', () => {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', () => {
            handleFiles(fileInput.files);
        });
        
        // معالجة الملفات المرفوعة
        function handleFiles(files) {
            if (files.length === 0) return;
            
            showLoading('جاري رفع الصور...');
            
            const formData = new FormData();
            
            // رفع كل ملف على حدة
            Array.from(files).forEach(file => {
                // التحقق من نوع الملف
                if (!file.type.match('image.*')) {
                    alert('يرجى رفع ملفات صور فقط');
                    return;
                }
                
                formData.append('file', file);
                
                // إرسال الملف إلى الخادم
                fetch('/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إضافة البطاقة إلى المصفوفة
                        const cardData = {
                            id: Date.now() + Math.random().toString(36).substr(2, 9),
                            filename: data.filename,
                            url: data.url,
                            width: data.width,
                            height: data.height,
                            x: 10,
                            y: 10
                        };
                        
                        uploadedCards.push(cardData);
                        
                        // إضافة صورة مصغرة
                        addThumbnail(cardData);
                    } else {
                        alert('حدث خطأ أثناء رفع الملف: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء رفع الملف');
                })
                .finally(() => {
                    hideLoading();
                    // إعادة تعيين نموذج الرفع
                    formData.delete('file');
                });
            });
        }
        
        // إضافة صورة مصغرة إلى القائمة
        function addThumbnail(cardData) {
            const thumbnailClone = document.importNode(thumbnailTemplate.content, true);
            const thumbnailImg = thumbnailClone.querySelector('img');
            
            thumbnailImg.src = cardData.url;
            thumbnailImg.dataset.id = cardData.id;
            
            // إضافة حدث النقر لإضافة البطاقة إلى الصفحة
            thumbnailImg.addEventListener('click', () => {
                addCardToPage(cardData);
            });
            
            thumbnailsContainer.appendChild(thumbnailClone);
        }
        
        // إضافة بطاقة إلى صفحة A4
        function addCardToPage(cardData) {
            const cardClone = document.importNode(cardItemTemplate.content, true);
            const cardItem = cardClone.querySelector('.card-item');
            const cardImg = cardClone.querySelector('img');
            const removeBtn = cardClone.querySelector('.remove-btn');
            
            // تعيين معرف البطاقة
            cardItem.dataset.id = cardData.id;
            
            // تعيين مصدر الصورة
            cardImg.src = cardData.url;
            
            // حساب الأبعاد بالملم
            const cardWidth = parseFloat(cardWidthInput.value);
            const cardHeight = parseFloat(cardHeightInput.value);
            
            // تعيين الأبعاد والموضع
            cardItem.style.width = cardWidth + 'mm';
            cardItem.style.height = cardHeight + 'mm';
            cardItem.style.left = cardData.x + 'mm';
            cardItem.style.top = cardData.y + 'mm';
            
            // إضافة حدث إزالة البطاقة
            removeBtn.addEventListener('click', () => {
                cardItem.remove();
            });
            
            // إضافة البطاقة إلى الصفحة
            a4Page.appendChild(cardItem);
            
            // تفعيل السحب والإفلات للبطاقة
            makeCardDraggable(cardItem);
        }
        
        // جعل البطاقة قابلة للسحب
        function makeCardDraggable(cardItem) {
            interact(cardItem).draggable({
                inertia: true,
                modifiers: [
                    interact.modifiers.restrictRect({
                        restriction: 'parent',
                        endOnly: true
                    })
                ],
                autoScroll: true,
                listeners: {
                    move: dragMoveListener
                }
            });
        }
        
        function dragMoveListener(event) {
            const target = event.target;
            
            // احصل على موضع X و Y الحالي من نمط CSS
            const x = parseFloat(target.style.left || '0');
            const y = parseFloat(target.style.top || '0');
            
            // تحديث الموضع
            target.style.left = (x + event.dx / 3.78) + 'mm';
            target.style.top = (y + event.dy / 3.78) + 'mm';
        }
        
        // ترتيب البطاقات تلقائيًا
        autoArrangeBtn.addEventListener('click', autoArrangeCards);
        
        function autoArrangeCards() {
            const cardItems = a4Page.querySelectorAll('.card-item');
            if (cardItems.length === 0) {
                alert('لا توجد بطاقات لترتيبها');
                return;
            }
            
            // الحصول على أبعاد البطاقة
            const cardWidth = parseFloat(cardWidthInput.value);
            const cardHeight = parseFloat(cardHeightInput.value);
            
            // أبعاد صفحة A4 بالملم
            const pageWidth = 210;
            const pageHeight = 297;
            
            // هوامش الصفحة
            const marginX = 10;
            const marginY = 10;
            
            // حساب عدد البطاقات في كل صف وعمود
            const cardsPerRow = Math.floor((pageWidth - 2 * marginX) / (cardWidth + 5));
            const cardsPerColumn = Math.floor((pageHeight - 2 * marginY) / (cardHeight + 5));
            
            // حساب المسافة بين البطاقات
            const spacingX = ((pageWidth - 2 * marginX) - (cardsPerRow * cardWidth)) / (cardsPerRow - 1 || 1);
            const spacingY = ((pageHeight - 2 * marginY) - (cardsPerColumn * cardHeight)) / (cardsPerColumn - 1 || 1);
            
            // ترتيب البطاقات
            cardItems.forEach((cardItem, index) => {
                const row = Math.floor(index / cardsPerRow);
                const col = index % cardsPerRow;
                
                const x = marginX + col * (cardWidth + spacingX);
                const y = marginY + row * (cardHeight + spacingY);
                
                cardItem.style.left = x + 'mm';
                cardItem.style.top = y + 'mm';
            });
        }
        
        // إنشاء ملف PDF
        generatePdfBtn.addEventListener('click', generatePdf);
        
        function generatePdf() {
            const cardItems = a4Page.querySelectorAll('.card-item');
            if (cardItems.length === 0) {
                alert('لا توجد بطاقات لإنشاء ملف PDF');
                return;
            }
            
            showLoading('جاري إنشاء ملف PDF...');
            
            // جمع بيانات البطاقات
            const cardsData = [];
            cardItems.forEach(cardItem => {
                const cardId = cardItem.dataset.id;
                const cardData = uploadedCards.find(card => card.id === cardId);
                
                if (cardData) {
                    cardsData.push({
                        filename: cardData.filename,
                        x: parseFloat(cardItem.style.left),
                        y: parseFloat(cardItem.style.top),
                        width: parseFloat(cardItem.style.width),
                        height: parseFloat(cardItem.style.height)
                    });
                }
            });
            
            // إرسال البيانات إلى الخادم
            fetch('/generate-pdf', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ cards: cardsData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إنشاء ملف PDF بنجاح');
                    // فتح الملف في نافذة جديدة
                    window.open(data.pdf_url, '_blank');
                } else {
                    alert('حدث خطأ أثناء إنشاء ملف PDF: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إنشاء ملف PDF');
            })
            .finally(() => {
                hideLoading();
            });
        }
        
        // إظهار شاشة التحميل
        function showLoading(text) {
            loadingText.textContent = text || 'جاري المعالجة...';
            loadingOverlay.classList.remove('d-none');
        }
        
        // إخفاء شاشة التحميل
        function hideLoading() {
            loadingOverlay.classList.add('d-none');
        }
    });
</script>
{% endblock %}