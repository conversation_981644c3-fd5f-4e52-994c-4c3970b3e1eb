# صانع بطاقات الطباعة الذكية

تطبيق ويب مبني بلغة بايثون وإطار Flask مع واجهة مستخدم متجاوبة باستخدام Bootstrap لترتيب وطباعة البطاقات بمقاسات دقيقة.

## الميزات الرئيسية

- واجهة مستخدم سهلة الاستخدام ومتجاوبة مع جميع الأجهزة
- تسجيل الدخول باستخدام حساب Google
- رفع صور البطاقات بتنسيقات مختلفة (JPG، PNG)
- ترتيب البطاقات على صفحات A4 بوضع عمودي (Portrait)
- إمكانية سحب وإفلات البطاقات لتغيير موضعها
- ترتيب تلقائي للبطاقات للاستفادة القصوى من مساحة الصفحة
- إنشاء ملف PDF جاهز للطباعة

## متطلبات النظام

- Python 3.8 أو أحدث
- متصفح ويب حديث
- اتصال بالإنترنت (لتسجيل الدخول باستخدام Google)

## التثبيت

1. قم بتنزيل أو استنساخ المشروع:

```bash
git clone https://github.com/yourusername/card-printer.git
cd card-printer
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:

```bash
python -m venv venv

# في نظام Windows
venv\Scripts\activate

# في نظام Linux/Mac
source venv/bin/activate
```

3. قم بتثبيت المتطلبات:

```bash
pip install -r requirements.txt
```

4. قم بإعداد متغيرات البيئة:

قم بإنشاء ملف `.env` في المجلد الرئيسي للمشروع وأضف المتغيرات التالية:

```
SECRET_KEY=your-secret-key-change-in-production
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

لإعداد مفاتيح Google API، قم بزيارة [Google Cloud Console](https://console.cloud.google.com/) وإنشاء مشروع جديد وتفعيل Google OAuth API.

5. قم بتشغيل التطبيق:

```bash
python app.py
```

6. افتح المتصفح وانتقل إلى `http://localhost:5000`

## الاستخدام

1. قم بتسجيل الدخول باستخدام حساب Google الخاص بك
2. انتقل إلى صفحة تصميم البطاقات
3. قم برفع صور البطاقات التي تريد طباعتها
4. اسحب وأفلت البطاقات لترتيبها على الصفحة، أو استخدم زر الترتيب التلقائي
5. قم بتعديل أبعاد البطاقات حسب الحاجة
6. انقر على زر "إنشاء PDF" للحصول على ملف جاهز للطباعة

## المساهمة

نرحب بمساهماتكم! إذا كنت ترغب في المساهمة في هذا المشروع، يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد للميزة التي تريد إضافتها (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات وحفظها (`git commit -m 'Add some amazing feature'`)
4. قم برفع التغييرات إلى الفرع الخاص بك (`git push origin feature/amazing-feature`)
5. قم بفتح طلب دمج (Pull Request)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للمزيد من التفاصيل.

## الاتصال

إذا كان لديك أي أسئلة أو اقتراحات، يرجى فتح مشكلة (Issue) في هذا المشروع أو التواصل معنا عبر البريد الإلكتروني.