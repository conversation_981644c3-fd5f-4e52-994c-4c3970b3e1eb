/* ملف الأنماط الرئيسي */

/* أنماط عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* تعديل اتجاه الأيقونات في النص العربي */
.fas, .far, .fab, .fa {
    margin-left: 0.25rem;
    margin-right: 0;
}

/* أنماط الأزرار */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1rem;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #4285F4;
    border-color: #4285F4;
}

.btn-primary:hover {
    background-color: #357ae8;
    border-color: #357ae8;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* أنماط البطاقات */
.card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* أنماط النماذج */
.form-control {
    border-radius: 5px;
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #4285F4;
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

/* أنماط التنقل */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    display: flex;
    align-items: center;
}

.navbar-brand img {
    margin-left: 0.5rem;
}

/* أنماط القوائم المنسدلة */
.dropdown-menu {
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    transition: all 0.2s;
}

.dropdown-item:hover {
    background-color: #f0f7ff;
    color: #4285F4;
}

/* أنماط الرسائل التنبيهية */
.alert {
    border-radius: 5px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* أنماط الشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .welcome-container {
        padding: 2rem;
    }
    
    .card-controls .row {
        flex-direction: column;
    }
    
    .card-controls .col-md-6:last-child {
        margin-top: 1rem;
        text-align: center;
    }
}