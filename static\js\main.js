/**
 * ملف JavaScript الرئيسي للتطبيق
 */

// التأكد من تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips في Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers في Bootstrap
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إضافة سلوك إغلاق التنبيهات
    document.querySelectorAll('.alert .close').forEach(function(button) {
        button.addEventListener('click', function() {
            this.parentElement.classList.add('fade');
            setTimeout(() => {
                this.parentElement.remove();
            }, 150);
        });
    });
    
    // تأثيرات التمرير السلس للروابط الداخلية
    document.querySelectorAll('a[href^="#"]:not([href="#"])').forEach(function(anchor) {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 70, // تعويض ارتفاع شريط التنقل
                    behavior: 'smooth'
                });
            }
        });
    });
});

/**
 * عرض رسالة تنبيه للمستخدم
 * @param {string} message - نص الرسالة
 * @param {string} type - نوع التنبيه (success, danger, warning, info)
 * @param {number} duration - مدة ظهور التنبيه بالمللي ثانية (اختياري)
 */
function showAlert(message, type = 'info', duration = 3000) {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.maxWidth = '90%';
    alertDiv.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
    
    // إضافة محتوى التنبيه
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
    `;
    
    // إضافة التنبيه إلى المستند
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه بعد المدة المحددة
    if (duration > 0) {
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => {
                alertDiv.remove();
            }, 150);
        }, duration);
    }
    
    // إضافة حدث النقر لزر الإغلاق
    alertDiv.querySelector('.btn-close').addEventListener('click', function() {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            alertDiv.remove();
        }, 150);
    });
    
    return alertDiv;
}

/**
 * تحويل حجم الملف إلى صيغة مقروءة
 * @param {number} bytes - حجم الملف بالبايت
 * @param {number} decimals - عدد الأرقام العشرية (اختياري)
 * @returns {string} - حجم الملف بصيغة مقروءة
 */
function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * التحقق من امتداد الملف
 * @param {string} filename - اسم الملف
 * @param {Array} allowedExtensions - مصفوفة الامتدادات المسموح بها
 * @returns {boolean} - صحيح إذا كان الامتداد مسموحًا به
 */
function checkFileExtension(filename, allowedExtensions) {
    const ext = filename.split('.').pop().toLowerCase();
    return allowedExtensions.includes(ext);
}