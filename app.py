import os
from flask import Flask, render_template, request, jsonify, send_from_directory, url_for
from flask_bootstrap import Bootstra<PERSON>
from werkzeug.utils import secure_filename
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
import uuid
from datetime import datetime
import io

# تهيئة التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'dev-key-for-testing'
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB max upload

# تأكد من وجود مجلد التحميلات
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# تهيئة Bootstrap
bootstrap = Bootstrap(app)

# التحقق من امتدادات الملفات المسموح بها
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# لوحة التحكم
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html', name='مستخدم')

# صفحة تصميم البطاقات
@app.route('/card-designer')
def card_designer():
    return render_template('card_designer.html')

# تحميل الصور
@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'لا يوجد ملف مرفق'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400

    if file and allowed_file(file.filename):
        # إنشاء اسم فريد للملف
        filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        # فتح الصورة وقراءة أبعادها
        img = Image.open(file_path)
        width, height = img.size

        return jsonify({
            'success': True,
            'filename': filename,
            'url': url_for('static', filename=f'uploads/{filename}'),
            'width': width,
            'height': height
        })

    return jsonify({'error': 'نوع الملف غير مسموح به'}), 400

# إنشاء ملف PDF
@app.route('/generate-pdf', methods=['POST'])
def generate_pdf():
    try:
        data = request.json
        cards = data.get('cards', [])

        if not cards:
            return jsonify({'error': 'لا توجد بطاقات لإنشاء ملف PDF'}), 400

        # إنشاء ملف PDF في الذاكرة
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)

        # أبعاد صفحة A4 بالملم
        _, page_height = A4

        # معالجة كل بطاقة
        for card_data in cards:
            img_path = os.path.join(app.config['UPLOAD_FOLDER'], card_data['filename'])
            if not os.path.exists(img_path):
                continue

            # فتح الصورة
            img = Image.open(img_path)

            # حساب موضع البطاقة على الصفحة
            x = card_data['x'] * mm
            y = page_height - (card_data['y'] * mm) - (card_data['height'] * mm)  # قلب الإحداثي Y
            width = card_data['width'] * mm
            height = card_data['height'] * mm

            # إضافة الصورة إلى PDF
            c.drawImage(img_path, x, y, width=width, height=height)

        # حفظ الملف
        c.save()

        # إعداد الاستجابة
        buffer.seek(0)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"cards_{timestamp}.pdf"

        return jsonify({
            'success': True,
            'message': 'تم إنشاء ملف PDF بنجاح',
            'pdf_url': url_for('download_pdf', filename=filename)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# تنزيل ملف PDF
@app.route('/download-pdf/<filename>')
def download_pdf(filename):
    # في التطبيق الحقيقي، يجب تخزين ملفات PDF وإدارتها بشكل صحيح
    # هذا مجرد مثال مبسط
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

if __name__ == '__main__':
    app.run(debug=True)