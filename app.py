import os
from flask import Flask, render_template, redirect, url_for, flash, request, session, jsonify
from flask_bootstrap import Bootstra<PERSON>
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from authlib.integrations.flask_client import OAuth
from werkzeug.utils import secure_filename
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
import uuid
import json
from datetime import datetime
import io

# تهيئة التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'dev-key-for-testing'
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB max upload

# تأكد من وجود مجلد التحميلات
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# تهيئة Bootstrap
bootstrap = Bootstrap(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# تهيئة OAuth لتسجيل الدخول بواسطة Google
oauth = OAuth(app)
google = oauth.register(
    name='google',
    client_id=os.environ.get('GOOGLE_CLIENT_ID'),
    client_secret=os.environ.get('GOOGLE_CLIENT_SECRET'),
    access_token_url='https://accounts.google.com/o/oauth2/token',
    access_token_params=None,
    authorize_url='https://accounts.google.com/o/oauth2/auth',
    authorize_params=None,
    api_base_url='https://www.googleapis.com/oauth2/v1/',
    client_kwargs={'scope': 'openid email profile'},
)

# نموذج المستخدم
class User(UserMixin):
    def __init__(self, id, name, email, profile_pic=None):
        self.id = id
        self.name = name
        self.email = email
        self.profile_pic = profile_pic

# قاعدة بيانات مؤقتة للمستخدمين (في الإنتاج يجب استخدام قاعدة بيانات حقيقية)
users_db = {}

@login_manager.user_loader
def load_user(user_id):
    return users_db.get(user_id)

# التحقق من امتدادات الملفات المسموح بها
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# تسجيل الدخول
@app.route('/login')
def login():
    redirect_uri = url_for('authorize', _external=True)
    return google.authorize_redirect(redirect_uri)

# معالجة تسجيل الدخول من Google
@app.route('/authorize')
def authorize():
    token = google.authorize_access_token()
    resp = google.get('userinfo')
    user_info = resp.json()
    
    # إنشاء مستخدم جديد أو تحديث معلومات المستخدم الحالي
    user_id = user_info['id']
    user = User(
        id=user_id,
        name=user_info['name'],
        email=user_info['email'],
        profile_pic=user_info.get('picture')
    )
    users_db[user_id] = user
    
    # تسجيل دخول المستخدم
    login_user(user)
    
    # إعادة توجيه إلى لوحة التحكم
    return redirect(url_for('dashboard'))

# تسجيل الخروج
@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('index'))

# لوحة التحكم
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html', name='مستخدم')

# صفحة تصميم البطاقات
@app.route('/card-designer')
@login_required
def card_designer():
    return render_template('card_designer.html')

# تحميل الصور
@app.route('/upload', methods=['POST'])
@login_required
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'لا يوجد ملف مرفق'}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400
    
    if file and allowed_file(file.filename):
        # إنشاء اسم فريد للملف
        filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # فتح الصورة وقراءة أبعادها
        img = Image.open(file_path)
        width, height = img.size
        
        return jsonify({
            'success': True,
            'filename': filename,
            'url': url_for('static', filename=f'uploads/{filename}'),
            'width': width,
            'height': height
        })
    
    return jsonify({'error': 'نوع الملف غير مسموح به'}), 400

# إنشاء ملف PDF
@app.route('/generate-pdf', methods=['POST'])
@login_required
def generate_pdf():
    try:
        data = request.json
        cards = data.get('cards', [])
        
        if not cards:
            return jsonify({'error': 'لا توجد بطاقات لإنشاء ملف PDF'}), 400
        
        # إنشاء ملف PDF في الذاكرة
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        
        # أبعاد صفحة A4 بالملم
        page_width, page_height = A4
        
        # معالجة كل بطاقة
        for card_data in cards:
            img_path = os.path.join(app.config['UPLOAD_FOLDER'], card_data['filename'])
            if not os.path.exists(img_path):
                continue
            
            # فتح الصورة
            img = Image.open(img_path)
            
            # حساب موضع البطاقة على الصفحة
            x = card_data['x'] * mm
            y = page_height - (card_data['y'] * mm) - (card_data['height'] * mm)  # قلب الإحداثي Y
            width = card_data['width'] * mm
            height = card_data['height'] * mm
            
            # إضافة الصورة إلى PDF
            c.drawImage(img_path, x, y, width=width, height=height)
        
        # حفظ الملف
        c.save()
        
        # إعداد الاستجابة
        buffer.seek(0)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"cards_{timestamp}.pdf"
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء ملف PDF بنجاح',
            'pdf_url': url_for('download_pdf', filename=filename)
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# تنزيل ملف PDF
@app.route('/download-pdf/<filename>')
@login_required
def download_pdf(filename):
    # في التطبيق الحقيقي، يجب تخزين ملفات PDF وإدارتها بشكل صحيح
    # هذا مجرد مثال مبسط
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename, as_attachment=True)

if __name__ == '__main__':
    app.run(debug=True)